---
title : "Tạo S3 Gateway endpoint"
date : 2023-10-25 
weight : 3
chapter : false
pre : " <b> 4.3 </b> "
---


1. <PERSON><PERSON><PERSON> cập vào [giao diện quản trị dịch vụ VPC](https://console.aws.amazon.com/vpc/home)
  + Click **Endpoints**.
  + Click **Create endpoint**.

2. Tại trang **Create endpoint**.
  + Tại mục **Name tag** điền **S3GW**.
  + Tại mục **Service Category** click chọn **AWS services**.
  + Tại ô tìm kiếm điền **S3**, sau đó chọn **com.amazonaws.[region].s3**

![S3](/images/4.s3/008-s3.png)

3. Tại mục **Services** chọn **com.amazonaws.[region].s3** có Type là **Gateway**.
  + <PERSON><PERSON><PERSON> mụ<PERSON> **VPC** , chọn **Lab VPC**.
  + <PERSON><PERSON><PERSON> mụ<PERSON> **Route tables**, chọ<PERSON> cả 2 route table.
  
![S3](/images/4.s3/009-s3.png)

4. <PERSON><PERSON><PERSON> chuột xuống dưới cùng, click **Create endpoint**.

Bước tiếp theo chúng ta sẽ tiến hành cấu hình Session Manager để có thể lưu trữ các session logs tới S3 bucket chúng ta đã tạo.