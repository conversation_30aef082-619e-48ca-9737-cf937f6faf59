---
title : "Session Management"
date : 2023-10-25
weight : 1
chapter : false
---
# Work with Amazon System Manager - Session Manager

### Overall
 In this lab, you'll learn the basics and practice of Amazon  System Manager - Session Manager
. Perform creating public and private instance connections. 

![ConnectPrivate](/images/arc-log.png) 

### Content
 1. [Introduction ](1-introduce/)
 2. [Preparation](2-prerequiste/)
 3. [Connect to EC2 instance](3-accessibilitytoinstances/)
 4. [Manage session logs](4-s3log/)
 5. [Port Forwarding](5-Portfwd/)
 6. [Clean up resources](6-cleanup/)
